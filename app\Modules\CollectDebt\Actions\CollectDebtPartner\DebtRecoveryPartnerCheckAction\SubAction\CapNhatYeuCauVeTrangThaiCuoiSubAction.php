<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction;

use Exception;
use App\Lib\Helper;
use App\Lib\ApiCall;
use App\Utils\CommonVar;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Symfony\Component\HttpFoundation\ParameterBag;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtReadyRecord;
use App\Modules\CollectDebt\Requests\v1\CollectDebtRequest\DebtRecoveryRequestCreditRequest;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreditAction\DebtRecoveryRequestCreditAction;

class CapNhatYeuCauVeTrangThaiCuoiSubAction
{
  public function run(CollectDebtPartner $collectDebtPartner): array
  {
    $collectDebtRequestTheoPartner = CollectDebtRequest::where('partner_request_id', $collectDebtPartner->partner_request_id)->first();
    
		// Yeu cau ghi so roi thi khong can cap nhat gi them nua
    if ($collectDebtRequestTheoPartner->isRequestDaGhiSo()) {

			$collectDebtPartner->description = 'Chạy vào luồng đã ghi sổ?';
      $collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_DA_XU_LY;
      $r = $collectDebtPartner->save();

			throw_if(!$r, new Exception('Không thể cập nhật partner về trạng thái đã xử lý'));

      return [
        'partner' => $collectDebtPartner,
        'is_excess' => 1,
      ];
    }


    $debtRecoveryRequestCredit = new DebtRecoveryRequestCreditRequest();
    
		$soTienKhaDungCuaPartner = $collectDebtPartner->getAmountBalance();
		$soTienCanThanhToanCuaYeuCau = $collectDebtRequestTheoPartner->amount_request;
		
		$soTienDungDeThanhToan = min($soTienKhaDungCuaPartner, $soTienCanThanhToanCuaYeuCau);

    $params = [
      'data' => [
        'partner_request_id'     => $collectDebtPartner->partner_request_id,
        'partner_transaction_id' => $collectDebtPartner->partner_transaction_id,
        'payment_account_id'     => $collectDebtPartner->payment_account_id,
				'amount_payment'				 => $soTienDungDeThanhToan,
        'fee'                    => $collectDebtPartner->fee,
      ]
    ];

    $debtRecoveryRequestCredit->setJson(new ParameterBag($params));
  
   
		$collectDebtRequest = app(DebtRecoveryRequestCreditAction::class)->run($debtRecoveryRequestCredit);

		if ( !$collectDebtRequest ) {
			$message = sprintf('env: %s - Cộng tiền vào RequestID: %s bị lỗi', config('app.env') ,$collectDebtPartner->partner_request_id);
			TelegramAlert::alertTienVe($message);
			throw new Exception($message);
		}
		
		$collectDebtPartner->number_perform = 99;
		$collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_DA_XU_LY;
		$collectDebtPartner->amount_payment += $soTienDungDeThanhToan;

		$collectDebtPartner->time_updated = time();
		$collectDebtPartner->updated_by = Helper::getCronJobUser();

		$collectDebtPartner->time_complated = time();
		$collectDebtPartner->complated_by = Helper::getCronJobUser();

		$collectDebtPartner->time_created_request = time();
		$collectDebtPartner->created_request_by = Helper::getCronJobUser();

		$collectDebtPartner->time_processing = time();
		$collectDebtPartner->processing_by = Helper::getCronJobUser();
		
		$collectDebtPartner->description = sprintf('Xử lý tiền về %s', Helper::priceFormat($collectDebtPartner->amount_payment));
		$savePartnerResult = $collectDebtPartner->save(); 
		
		if (!$savePartnerResult) {
			throw new Exception("PartnerId: $collectDebtPartner->id khong the saved ve trang thai cuoi cung");
		}

		$readyRecord = app(CreateReadyRecordSubAction::class)->run(
			$collectDebtPartner->partner_request_id, 
			$collectDebtPartner->contract_code
		);

		if (!$readyRecord || empty($readyRecord->id)) {
			throw new Exception("PartnerId: $collectDebtPartner->id khong the tao ready record");
		}

    return [
      'partner' => $collectDebtPartner,
      'is_excess' => 0,
    ];
  }
} // End class