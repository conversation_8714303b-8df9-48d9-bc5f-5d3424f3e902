<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSearchDataAction;

use Carbon\Carbon;
use App\Lib\Helper;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtShare;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\Task\GroupScheduleIntoRequestTask;

class DebtRecoveryRequestSearchDataAction
{
  public function run(Request $request)
  {
    $collectDebtRequestPaginate = CollectDebtRequest::query();

		// ma hd
    $contractCode = $request->json('data.filter.contract_code');
    if (!empty($contractCode)) {
      $collectDebtRequestPaginate = $collectDebtRequestPaginate->where('contract_code', $contractCode);
    }

		// profile id
		$profileIds = $request->json('data.filter.profile_ids');
    if (!empty($profileIds)) {
			$collectDebtRequestPaginate = $collectDebtRequestPaginate->whereIn('profile_id', $profileIds);
    }

    // ma yeu cau, ma chung tu
    $partnerRequestTransactionId = $request->json('data.filter.partner_request_transaction_id');
    if (!empty($partnerRequestTransactionId)) {
      $collectDebtRequestPaginate = $collectDebtRequestPaginate->where(function ($query) use ($partnerRequestTransactionId) {
        $query->where('partner_request_id', $partnerRequestTransactionId)
          ->orWhere('partner_transaction_id', $partnerRequestTransactionId);
      });
    }

    // phuong thuc thu hoi
    $paymentMethodCode = $request->json('data.filter.payment_method_code');
    if (!empty($paymentMethodCode)) {
      $collectDebtRequestPaginate = $collectDebtRequestPaginate->where('payment_method_code', $paymentMethodCode);
    }

    // Status
    $status = $request->json('data.filter.status');
    if (!empty($status)) {
      $collectDebtRequestPaginate = $collectDebtRequestPaginate->where('status', $status);
    }

    // Status Payment
    $statusPayment = $request->json('data.filter.status_payment');
    if (!empty($statusPayment)) {
      $collectDebtRequestPaginate = $collectDebtRequestPaginate->where('status_payment', $statusPayment);
    }

    // Status recoreded
    $statusRecored = $request->json('data.filter.status_recored');
    if (!empty($statusRecored)) {
      $collectDebtRequestPaginate = $collectDebtRequestPaginate->where('status_recored', $statusRecored);
    }

    $requestId = $request->json('data.filter.request_id');
    if (!empty($requestId)) {
      $collectDebtRequestPaginate = $collectDebtRequestPaginate->where('id', $requestId);
    }

		$fromDate = $request->json('data.filter.from_date');
		if (!empty($fromDate)) {
			$fromDateTimestamp = Carbon::createFromFormat('d-m-Y', $fromDate)->startOfDay()->timestamp;
			$collectDebtRequestPaginate = $collectDebtRequestPaginate->where('time_created', '>=', $fromDateTimestamp);
		}

		$toDate = $request->json('data.filter.to_date');
		if (!empty($toDate)) {
			$toDateTimestamp = Carbon::createFromFormat('d-m-Y', $toDate)->endOfDay()->timestamp;
			$collectDebtRequestPaginate = $collectDebtRequestPaginate->where('time_created', '<=', $toDateTimestamp);
		}

		// Lọc ra các giao dịch trích được tiền: data.filter.has_amount_receiver (1: trích được, 0: không trích được
		if ($request->json('data.filter.has_amount_receiver', 0) == 1) {
			$collectDebtRequestPaginate = $collectDebtRequestPaginate->where('amount_receiver', '>', 0);
		}

		$hinhThucTrich = $request->json('data.filter.create_from');
		if (!empty($hinhThucTrich)) {
			$collectDebtRequestPaginate = $collectDebtRequestPaginate->where('create_from', $hinhThucTrich);
		}

		$fields = [
			"id", 
			"type", 
			"profile_id", 
			"contract_code", 
			"plan_ids", 
			"payment_method_code", 
			"payment_channel_code", 
			"payment_account_id", 
			"payment_account_holder_name", 
			"payment_account_bank_code", 
			"payment_account_bank_branch", 
			"partner_request_id", 
			"partner_transaction_id", 
			"currency", 
			"amount_request", 
			"amount_payment", 
			"amount_receiver", 
			"fee", 
			"time_begin", 
			"time_expired", 
			"is_payment", 
			"status", 
			"status_payment", 
			"status_recored", 
			
			"other_data", 
			
			"create_from", 
			"time_checked",
			"time_created", 
			"time_updated", 
			"time_approved", 
			"time_canceled", 
			"time_canceled_payment", 
			"time_completed", 
			"time_completed_recheck", 
			"time_recored", 
			"time_sended", 
			"time_receivered", 
			"created_by", 
			"updated_by", 
			"approved_by", 
			"canceled_by", 
			"canceled_payment_by", 
			"completed_by", 
			"completed_recheck_by", 
			"recored_by", 
			"sended_by", 
			"receivered_by", 
			"checked_by", 
		]; 

		if ($request->json('data.is_export')) {
			$fields = array_merge($fields, [
				'plan_data',
				"version", 
				"description", 
			]);
		}
		
    $collectDebtRequestPaginate = $collectDebtRequestPaginate->orderBy('id', 'DESC');

		$isNeedFullPage = Str::contains($request->path(), 'DebtRecoveryRequestSearchData');

		if ($isNeedFullPage) {
			$collectDebtRequestPaginate = $collectDebtRequestPaginate->paginate( 
				$request->json('data.limit', 10), 
				['*'], 
				'page', 
				$request->json('data.page', 1) 
			);
		}else {
			$collectDebtRequestPaginate = $collectDebtRequestPaginate->simplePaginate(
        $request->json('data.limit', 10),
        $fields,
        'page',
        $request->json('data.page', 1)
      );
		}

    $collection = $collectDebtRequestPaginate->getCollection();

    $requestIds = $collection->pluck('id')->toArray();


    $ledgerRequestInfo = $this->getLedgers($requestIds);
    
    $collectDebtShare = null;

    if ($request->json('data.is_export')) {
      $collectDebtShare = CollectDebtShare::query()->where('contract_code', $request->json('data.filter.contract_code'))->first();
    }

    $collection->transform(function (CollectDebtRequest $collectDebRequest) use ($ledgerRequestInfo, $collectDebtShare) {
			if ($collectDebRequest->isYeuCauCoYeuToHuy()) {
				$collectDebRequest->status = CollectDebtEnum::REQUEST_STT_TU_CHOI;
			}
			
      $can[] = CollectDebtEnum::CAN_VIEW_DETAIL_REQUEST;

      if ($collectDebRequest->isCoTheCheckPartner()) {
        $can[] = CollectDebtEnum::CAN_CHECK_PARTNER;
      }

      if ($collectDebRequest->isYeuCauTrichTuDong()) {
        if ($collectDebRequest->isCoTheTuChoi() && $collectDebRequest->isYeuCauChuaHetHan() && empty($collectDebRequest->time_canceled)) {
          $can[] = CollectDebtEnum::CAN_CANCEL_AUTO_REQUEST;
        }
      }

      if ($collectDebRequest->isYeuCauTrichTay()) {
        if ($collectDebRequest->isChuaDuyetTrichTayThuCong()) {
          $can[] = CollectDebtEnum::CAN_APPROVE_MANUAL_REQUEST;
          $can[] = CollectDebtEnum::CAN_CANCEL_MANUAL_REQUEST;
        }

				if ($collectDebRequest->isTrichTayThuCongThongThuong()) {
					if(!$collectDebRequest->isRecorded()) {
						$can[] = CollectDebtEnum::CAN_CANCEL_MANUAL_REQUEST;
						// $can[] = CollectDebtEnum::CAN_CANCEL_AUTO_REQUEST;
					}
				}
      }

      if ($collectDebRequest->isTrichTayGiamPhiVaChuaDuyetBuoc1() && $collectDebRequest->isTrichNgay() != 'YES') {
        $can[] = CollectDebtEnum::CAN_APPROVE_REDUCE_FEE_STEP_1;
        $can[] = CollectDebtEnum::CAN_CANCEL_MANUAL_REQUEST;
      }

      if ($collectDebRequest->isTrichTayGiamPhiVaChuaDuyetBuoc2() && $collectDebRequest->isTrichNgay() != 'YES') {
        $can[] = CollectDebtEnum::CAN_APPROVE_REDUCE_FEE_STEP_2;
        $can[] = CollectDebtEnum::CAN_CANCEL_MANUAL_REQUEST;
      }

      if ($collectDebRequest->isRecorded()) {
        $can[] = CollectDebtEnum::CAN_VIEW_REQUEST_ON_LEDGER;
      }

			if ($collectDebRequest->isLenhTrichNgayDaHachToanNhungChuaBiHuyVaChuaCoKetQuaTrich()) {
				$can[] = CollectDebtEnum::CAN_CANCEL_LENH_RUT_TIEN_NHANH;
			}

			if ($collectDebRequest->isCoTheTaoDieuChinh()) {
				$can[] = CollectDebtEnum::CAN_CREATE_ADJUSTMENT_REQUEST;
			}

			if ($collectDebRequest->isYeuCauCoYeuToHuy() && $collectDebRequest->isPaymentViaChannelMpos()) {
				$can[] = CollectDebtEnum::CAN_RE_CANCEL;
			}

			$can = array_values(array_unique($can));
			
      $collectDebRequest->can = $can;

      $collectDebRequest->total_amount_paid = 0;
      $collectDebRequest->total_debt_excess = 0;


      if (isset($ledgerRequestInfo[$collectDebRequest->id])) {
        $collectDebRequest->total_amount_paid = $ledgerRequestInfo[$collectDebRequest->id]['total_amount_paid'];
        $collectDebRequest->total_debt_excess = $ledgerRequestInfo[$collectDebRequest->id]['total_debt_excess'];
      }

			
      $collectDebRequest->late_partner_wording = $ledgerRequestInfo[$collectDebRequest->id]['late_partner_wording'] ?? '';
      $collectDebRequest->late_partner_wording_class = $ledgerRequestInfo[$collectDebRequest->id]['late_partner_wording_class'] ?? '';
			$collectDebRequest->accounting_info = $ledgerRequestInfo[$collectDebRequest->id]['accounting_info'] ?? [];

			$collectDebRequest->is_trich_ngay = false;
			if ($collectDebRequest->isManualDebt() && $collectDebRequest->isTrichNgay() == 'YES') {
				$collectDebRequest->is_trich_ngay = true;
			}
			
      // Export
      if ($collectDebtShare) {
        $collectDebRequest->contract_type_text = $collectDebtShare->getLoaiHopDongText();
        $profile = $collectDebtShare->getProfileDataAsArray();

        $collectDebRequest->merchant = [
          'fullname' => $profile['merchant']['fullname'],
          'email' => $profile['merchant']['email']
        ];

				return $collectDebRequest;
      }

      return $collectDebRequest->makeHidden([
				"time_checked",
				"time_created", 
				"time_updated", 
				"time_approved", 
				"time_canceled", 
				"time_canceled_payment", 
				"time_completed", 
				"time_completed_recheck", 
				"time_recored", 
				"time_sended", 
				"time_receivered", 
				"created_by", 
				"updated_by", 
				"approved_by", 
				"canceled_by", 
				"canceled_payment_by", 
				"completed_by", 
				"completed_recheck_by", 
				"recored_by", 
				"sended_by", 
				"receivered_by", 
				"checked_by", 
				"time_begin_as_date",
				"time_expired_as_date",
				"time_begin_as_vn_date",
				"time_expired_as_vn_date"
			]);
    });

    $collectDebtRequestPaginate->setCollection($collection);

    return $collectDebtRequestPaginate;
  }

  public function getLedgers(array $requestIds = [])
  {
    $ledgers = CollectDebtLedger::whereIn('request_id', $requestIds)
      ->select([
				'id', 
				'other_data',
				'request_id', 
				'amount', 
				'time_created', 
				'status',
				'status_plan',
				'status_summary',
			])
      ->get();
			
    $returnData = $ledgers->filter(function (CollectDebtLedger $ledger) {
			return !$ledger->isSoDoiTacBaoMuon();
		})->map(function (CollectDebtLedger $ledger) {
			$summary = $ledger->getOtherDataItem('SUMMARY');

			$totalDebtExcess = 0;
			$hasExcess = $ledger->hasOtherDataItem('EXCESS');

			if ($hasExcess) {
				$excess = $ledger->getOtherDataItem('EXCESS');
				$totalDebtExcess = collect($excess['data'])->sum('amount_excess');
			}

			$accoutingInfo = [];
			if ($ledger->status == CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN) {
				$accoutingInfo[] = 'Hạch toán';
			}

			if ($ledger->status_plan == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT) {
				$accoutingInfo[] = 'Làm lại lịch';
			}

			if ($ledger->status_summary == CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT) {
				$accoutingInfo[] = 'Tổng hợp số liệu';
			}

			$latePartnerWording = $latePartnerWordingClass = '';
			
			
			if (!empty($summary['data']['fee_overdue'])) {
				$latePartnerWording = 'Quá hạn';
				$latePartnerWordingClass = 'label-danger';
			}

			if (!empty($summary['data']['fee_overdue_cycle'])) {
				$latePartnerWording = 'Chậm kỳ';
				$latePartnerWordingClass = 'label-warning';
			}

			return [
				'request_id' => $ledger->request_id,
				'total_amount_paid' => $summary['data']['total_amount_paid'] ?? 0,
				'total_debt_excess' => $totalDebtExcess ?? 0,
				'late_partner_wording' => $latePartnerWording,
				'late_partner_wording_class' => $latePartnerWordingClass,
				'accounting_info' => $accoutingInfo
			];
    })->keyBy('request_id')->toArray();

    $ledgersByRequest = $ledgers->groupBy('request_id');

    foreach ($returnData as $requestId => &$requestData) {
      $ledgerOfRequest = $ledgersByRequest->get($requestId);
			
      $ledgerBaoMuon = $ledgerOfRequest->first(function (CollectDebtLedger $lg) {
        return $lg->isSoDoiTacBaoMuon();
      });

      if ($ledgerBaoMuon && empty($requestData['late_partner_wording'])) {
				$requestData['late_partner_wording_class'] = 'label-info';
        $requestData['late_partner_wording'] = sprintf('Đối tác báo muộn: %s', Helper::priceFormat($ledgerBaoMuon->amount, 'đ'));
      }
    }

    return $returnData;
  }
} // End class