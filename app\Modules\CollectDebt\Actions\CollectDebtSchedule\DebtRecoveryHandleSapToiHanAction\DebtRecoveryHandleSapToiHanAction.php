<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction;

use App\Lib\Helper;
use Exception;
use App\Lib\TelegramAlert;
use Illuminate\Http\Request;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\ReplaceNoiDungBySummarySubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\TaoEventMailSapToiHanTatToanSubAction;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\GetTemplateMailSubAction;

class DebtRecoveryHandleSapToiHanAction
{
	private array $__hopDongDaXuLySapToiHanTatToan = [];

	public function run()
	{
		$info = sprintf(
			'env: %s ----- Type: MAIL_SAP_TOI_HAN_TAT_TOAN --- LogId: %s', 
			config('app.env'), 
			request('api_request_id', '')
		);

		@TelegramAlert::sendEmails($info);
		
		$whereRaw = sprintf("DATEDIFF( DATE(FROM_UNIXTIME(contract_time_end)), '%s') IN (5,3,2,1)", now()->format('Y-m-d'));

		$collectDebtGuides = CollectDebtGuide::query()
																				 ->whereRaw($whereRaw)
																				 ->whereDoesntHave('collectDebtEvents', function ($query) {
																					return $query->where('service_care_code', 'MAIL')
																											 ->where('category_care_code', 'NOTIFY_CONTRACT_DUE')
																											 ->whereBetween('time_created', [
																													now()->startOfDay()->timestamp,
																													now()->endOfDay()->timestamp,
																											 ]);
																				 })
																				 ->whereHas('collectDebtSummary', function ($query) {
																					 $query->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN);
																				 })
																				 ->get();
		
		if ($collectDebtGuides->isEmpty()) {
			throw new Exception('Khong co thong tin HD sap toi han tat toan');
			return;
		}

		$collectDebtGuides =  $collectDebtGuides->load(['collectDebtSummary', 'collectDebtShare']);

		$emailTemplateContentTheoNgay = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => 'NOTIFY_CONTRACT_DUE',
			'customer_service_care_code' => 'MAIL',
		]);

		$emailTemplateContentTheoKy = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => 'NOTIFY_CONTRACT_DUE_PERIOD',
			'customer_service_care_code' => 'MAIL',
		]);
		
		foreach ($collectDebtGuides as $collectDebtGuide) {
			if (@$collectDebtGuide->collectDebtSummary->status_contract == CollectDebtEnum::SUMMARY_STATUS_CONTRACT_DA_TAT_TOAN) {

				continue;
			}

			try {
				
				// Tao event doan nay
				$collectDebtEvent = app(TaoEventMailSapToiHanTatToanSubAction::class)->run(
					$collectDebtGuide->collectDebtShare,
					$collectDebtGuide->collectDebtSummary,
				);
				
				// Replace bien vao trong noi dung email
				$emailContent = '';
	
				if ($collectDebtGuide->isChiDanHopDongTrichNgay()) {
					if (empty($emailTemplateContentTheoNgay['content'])) {
						continue;
					}

					$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
						$collectDebtEvent,
						$collectDebtGuide->collectDebtSummary,
						$emailTemplateContentTheoNgay
					);
				}
	
				if ($collectDebtGuide->isChiDanHopDongTrichKy()) {
					if (empty($emailTemplateContentTheoKy['content'])) {
						continue;
					}

					$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
						$collectDebtEvent,
						$collectDebtGuide->collectDebtSummary,
						$emailTemplateContentTheoKy
					);
				}
				
				if (empty($emailContent)) {
					continue;
				}
	
				$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
				$collectDebtEvent->content = $emailContent;
				$savedResult = $collectDebtEvent->save();
	
				if (!$savedResult) {
					continue;
				}
	
				$this->__hopDongDaXuLySapToiHanTatToan[] = $collectDebtEvent->contract_code;
			}catch(\Throwable $th) {
				continue;
			}
		};

		@TelegramAlert::sendEmails("Sap Toi Han Tat Toan: " . json_encode($this->__hopDongDaXuLySapToiHanTatToan, JSON_UNESCAPED_UNICODE));
		return $this->__hopDongDaXuLySapToiHanTatToan;
	}
} // End class