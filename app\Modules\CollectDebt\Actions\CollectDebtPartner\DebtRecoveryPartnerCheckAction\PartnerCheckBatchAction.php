<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction;

use Exception;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Ultilities\BatchUtil;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Requests\v1\CollectDebtLedger\DebtRecoveryLedgerCreateRequest;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerCreateAction\SubAction\GhiSoChoYeuCauDaCoSoSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\TaoYeuCauKhongCanGuiDoiTacSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\CapNhatYeuCauVeTrangThaiCuoiSubAction;

class PartnerCheckBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}
	
	public function run()
	{
		$r = CollectDebtPartner::query()
													 ->join('debt_recovery_summary', 'debt_recovery_summary.contract_code', '=', 'debt_recovery_partner.contract_code')
													 ->where('debt_recovery_partner.status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
													 ->limit(config('nextlend.BATCHING_LIMIT'))
													 ->pluck('debt_recovery_partner.id')
													 ->toArray();

		if ( empty($r) ) {
			return ['msg' => 'No data for PartnerCheckBatchAction'];
		}

		$ranges = array_chunk($r, $this->batchSize);

		$this->processBatch($ranges);

		return ['msg' => 'ok done'];
	}

	
	private function processBatch($ranges)
	{
		$baseUrl = config('app.url');

		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/ProcessPartner?ids=%s', $baseUrl, implode(',', $r));
				yield $r => new Request('GET', $url);
			}
		};

		$client = $this->createHttpClient($this->timeout - 10);

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, $id) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[PartnerCheck ---> failed: " . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return true;
	}

	public function ProcessPartner() {
		$ids = request()->get('ids');
		$listIds = array_map('intval', explode(',', $ids));

		$listPartner = CollectDebtPartner::query()
																		 ->with('partnerOnSummary:id,contract_code')
																		 ->whereIn('id', $listIds)
																		 ->where('status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
																		 ->get();
		
		if ($listPartner->isEmpty()) {
			return 'No data';
		}

		foreach ($listPartner as $p) {
			try {
				$this->HandlePartnerCheck($p);
			}catch(\Throwable $th) {
				continue;
			}
		}
	}

	public function HandlePartnerCheck(CollectDebtPartner $collectDebtPartner)
	{
		$id = $collectDebtPartner->id;
		
		if (empty($collectDebtPartner->contract_code)) {
			return "PartnerId: $collectDebtPartner->id khong co ma hop dong";
		}

		if (!$collectDebtPartner->partnerOnSummary) {
			return "PartnerId: $collectDebtPartner->id khong ton tai summary";
		}

		if ($collectDebtPartner->status != CollectDebtEnum::PARTNER_STT_CHUA_XU_LY) {
			return "PartnerId: $collectDebtPartner->id is processing";
		}

		// Bắt case đặc biệt cho nguồn tiền khác MPOS
		if (in_array($collectDebtPartner->payment_method_code, ['IB_OFF', 'WALLET', 'VIRTUALACCOUNT'])) {
			$soDuCanTru = $collectDebtPartner->getAmountBalance();
			if ($soDuCanTru <= 0) {
				return "PartnerId: $collectDebtPartner->id khong thoa man dieu kien check";
			}
		}

		// Update lên thành đang xử lý
		$wasUpdateProcessing = CollectDebtPartner::query()
			->where('id', $collectDebtPartner->id)
			->where('status', CollectDebtEnum::PARTNER_STT_CHUA_XU_LY)
			->update([
				'status' => CollectDebtEnum::PARTNER_STT_DANG_XU_LY,
				'number_perform' => 1
			]);

		if ( !$wasUpdateProcessing ) {
			throw new Exception('Loi khong the update partner thanh PROCESSING...');
		}

		DB::beginTransaction();
		
		try {
			if ($collectDebtPartner->isTonTaiYeuCau()) {
				$collectDebtPartnerCreditRequest = app(CapNhatYeuCauVeTrangThaiCuoiSubAction::class)->run($collectDebtPartner);

				/**
				 * Xuống đến đây mà partner vẫn còn `ĐANG XỬ LÝ`, chứng tỏ bị lỗi gì đó 
				 * -> UPDATE lại thành CHƯA XỬ LÝ và break;
				 */
				if ($collectDebtPartnerCreditRequest['partner']->isPartnerDangXuLy()) {
					mylog(['Da bi loi gi do ma khong the cap nhat ve trang thai cuoi' => 'ok']);
					throw new Exception('Loi khong cap nhat duoc yeu cau ve trang thai cuoi');
				}

				// Nếu yc đã ghi sổ rồi và ghi nhận số tiền thừa, bắt !empty để lịch thu quá khứ ko bị ghi sổ 2 lần
				$collectDebtRequest = $collectDebtPartner->collectDebtRequest;
				if (!empty($collectDebtPartnerCreditRequest['is_excess']) && !empty($collectDebtRequest->partner_transaction_id)) {
					$inputs = [
						'data' => [
							'profile_id'    => $collectDebtRequest->profile_id,
							'contract_code' => $collectDebtRequest->contract_code,
							'plan_ids'      => $collectDebtRequest->plan_ids,
							'request_id'    => $collectDebtRequest->id,
							'currency'      => $collectDebtRequest->currency,
							'amount'        => $collectDebtPartner->amount_receiver,
							'description'   => 'TRICH_MUON',
							'status'        => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY,
							'time_record'   => time(),
							'created_by'    => Helper::getCronJobUser(),
							'time_created'  => time(),
						]
					];

					$rq = new DebtRecoveryLedgerCreateRequest();
					$rq->setJson(new \Symfony\Component\HttpFoundation\ParameterBag((array) $inputs));

					app(GhiSoChoYeuCauDaCoSoSubAction::class)->run($collectDebtRequest, $rq, $collectDebtPartner);
				}

				DB::commit();


				return $id;
			} // End if is ton tai yeu cau

			if ($collectDebtPartner->isKhongTonTaiYeuCau()) {
				mylog([
					'Cong no KHONG CO yêu cầu' => 'YES',
					'Cong no dang xu ly, ma chung tu la:' => $collectDebtPartner->partner_transaction_id
				]);

				$collectDebtPartnerResult = app(TaoYeuCauKhongCanGuiDoiTacSubAction::class)->run($collectDebtPartner);
				DB::commit();

				return $id;
			}

			throw new Exception('Không biết là lỗi gì...');
		} catch (\Throwable $th) {
			DB::rollBack();

			// Khi ban ghi bi loi, thi can tra lai trang ban dau (CHUA XU LY)
			$updateChuaXuLy = CollectDebtPartner::query()->where('id', $collectDebtPartner->id)
																 ->where('status', CollectDebtEnum::PARTNER_STT_DANG_XU_LY)
																 ->update([
																	'status' => CollectDebtEnum::PARTNER_STT_CHUA_XU_LY,
																	'number_perform' => 0,
																 ]);

			if (!$updateChuaXuLy) {
				mylog(['[LOI UPDATE VE CHUA XU LY]' => $updateChuaXuLy]);
			}

			throw new Exception('Loi xu ly partner: ' . Helper::traceError($th));
		}

		return $id;
	}
}  // End class