[2025-09-18 13:24:32] local.INFO: ProcessRecored {"request":{"ids":"931,960","api_request_id":"DEBT_09181324_1wOjc8"}} 
[2025-09-18 13:51:27] local.INFO: ProcessRecored {"request":{"ids":"961","api_request_id":"DEBT_09181351_9z3Z1A"}} 
[2025-09-18 13:51:49] local.INFO: ProcessRecored {"request":{"ids":"961","api_request_id":"DEBT_09181351_l7r8on"}} 
[2025-09-18 13:51:50] local.INFO: ProcessRecored {"request":{"ids":"961","api_request_id":"DEBT_09181351_aPtaqX"}} 
[2025-09-18 13:57:55] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09181357_2L8qf2"}} 
[2025-09-18 13:59:53] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09181359_DhuiEl"}} 
[2025-09-18 13:59:54] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09181359_EHjlOZ"}} 
[2025-09-18 13:59:54] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09181359_eR3B2G"}} 
[2025-09-18 13:59:55] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09181359_HadUph"}} 
[2025-09-18 14:00:03] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09181400_QKqbcG"}} 
[2025-09-18 14:00:12] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09181400_xoZZaT"}} 
[2025-09-18 14:00:16] local.INFO: SendRequestBatchAction {"request":{"api_request_id":"DEBT_09181400_exYH2P"}} 
[2025-09-18 14:00:16] local.INFO: HandleSendRequestMpos {"request":{"ids":"3253","api_request_id":"DEBT_09181400_7gzUrA"}} 
[2025-09-18 14:00:17] local.INFO: partner-collect-debt-gateway/send-debt {"request":{"data":{"partner_request_id":"NL25091861773","partner_merchant_id":"22732","amount_payment":484999,"contract_code":"TEST-C50E1T-L3","loan_original_amount":9200000,"deduction_per_day_amount":1000000,"loan_balance":10000000,"request_id":61773,"users_admin_id":"cronjob","time_begin":1758178260,"time_end":1758207600,"payment_channel":"MPOS"},"checksum":"c91e7348a105f461fb6792256bf12cdc","channel_code":null,"time_request":1758178816,"version":"1.0","api_request_id":null}} 
[2025-09-18 14:00:17] local.INFO: [sendDebt] ---->NL25091861773 {"func":"sendDebt","inputRaw":{"nextlend_request_id":"NL25091861773","merchantId":"22732","lendingRequestId":"NL25091861773","debitAmount":484999,"requestTime":"20250918140017","lendingId":"TEST-C50E1T-L3","loanOriginalAmount":9200000,"deductionPerDayAmount":1000000,"loanBalance":10000000,"partner_code":"MPOS","partnerCode":"NEXT_LEND","_prefix":"","recoverEndTime":"20250918220000999","recoverStartTime":"20250918135100000"},"requestInput":{"Fnc":"sendDebt","Checksum":"1051f5a23e7918e7bba9841a1d8a9271","EncData":"2uU0uOeXCKUXeiyhiQ83IY341XtrZNBzPzkJTbTTbqnzkR7IhO7s4G/UA+qx/Jibg7a+KWq/DY+/uHf2SSbcZeD3lzD3dYadTdprDYvEtW96Qu49OlQkKYdC4a5fg+AOGcsor4DsM9lYXKUf1B+KXH1kxgRsoL/8PuPsff37vI6I8evlV7PoIKdJ24qec1qZeJbt0LcINpKfPKfERO0f55tbQCrMNCGPIN7rQ7wO/rkSOEotlHMa3J+7JmWPV7RY9e3QtdkhFXx7UlsRuo6FuYoo4vl1LbLBfyTlPsf7llG5wFxSqXjIDWi6it0yQ01HGbJPD6MNZxAJf47/veH+v+VOLja83dVwaq9r5wesl1KoyAN+395E41kjWrGGQMvvePlAp0Fv2TgDixEaDRaaOEmZ2XhnkhkN/2GiJY1iecTFHkj4RD1/+T93MDZAs69Ye9nQfKfdLNxQmfX5vRp3Qha+fjNGhjzaMF0ghEFEyfxhOkdM0V0v1pHPdQTDStFGd2R7Bkjmw4HsxxOa+6nx7s1fx220W3rPi9I5rMLkNtM=","ChannelCode":"NEXTLENDV4","Version":"1.0"},"Result":{"Fnc":"sendDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":-46001,"data":"{\"errorCode\":-46001,\"data\":\"{\\\"status\\\":false,\\\"error\\\":{\\\"code\\\":-46001,\\\"message\\\":\\\"L\\\\u1ed7i -46001: Reference ID duplicate\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"676c7fcbc761bba3936ebfc745ba0ffc","Description":"ERROR"},"ParseError":{"errorCode":-46001,"data":"{\"status\":false,\"error\":{\"code\":-46001,\"message\":\"L\\u1ed7i -46001: Reference ID duplicate\",\"messageEn\":\"\"}}"}} 
[2025-09-18 14:00:18] local.INFO: partner-collect-debt-gateway/check-debt {"request":{"data":{"partner_request_id":"NL25091861773","partner_merchant_id":"22732","amount_payment":484999,"contract_code":"TEST-C50E1T-L3","loan_original_amount":9200000,"deduction_per_day_amount":1000000,"loan_balance":10000000,"request_id":61773,"users_admin_id":"{\"id\":\"cronjob\",\"username\":\"cronjob\",\"mobile\":\"cronjob\"}","payment_channel":"MPOS","amount":484999,"partner_save":1},"checksum":"07023d46222f32a202ff8e97a292f5b9","channel_code":null,"time_request":1758178818,"version":"1.0","api_request_id":null}} 
[2025-09-18 14:00:19] local.INFO: [checkDebt] ---->NL25091861773 {"func":"checkDebt","inputRaw":{"nextlend_request_id":"NL25091861773","partner_code":"MPOS","merchantId":"22732","lendingRequestId":"NL25091861773","debitAmount":484999,"requestTime":"20250918140018","lendingId":"TEST-C50E1T-L3","loanOriginalAmount":9200000,"deductionPerDayAmount":1000000,"loanBalance":10000000,"_prefix":""},"requestInput":{"Fnc":"checkDebt","Checksum":"b2d13087f06ee6c9563b2a4170ac61b1","EncData":"wiDPm3M2n0pbKa61+Mttr7Qe723OSOAZz5Pa+0zmWgui7WFDarX83/w3TfJeeNS13RREppasd1VvcWuaDvjzQKjFRp1sHQk8s4J3X19elEYk7kBJtaPoZNvESEKAEYKG+bKgbRyhMHuAA2XftjZUKC9NirHrOOsZKvQnaDWLnVS8aw3ziw9wT57g+5cFLSUrYX3JlxZCiR++U9ey+wutIL9Rxu/VeBGOe4If9+UQ4fpI0XVGTkY4HnroXJwuAd6JXDkemqTGAF8VD/AHUxRGBtq+whRvRDppgBXpQEC/sKz9slk13oLjujVIr05O+34oFnyd7QW354JNfK7m101FJF/UGHg54UFfXB07nW5dT8mvhpZc52jh+yhDtCcHIzb4i1F9Sk1p1QxJSNVRI09mlPxZmf3GSAefKAgE4WhkH2U=","ChannelCode":"NEXTLENDV4","Version":"1.0"},"Result":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":22732,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25091861773\\\",\\\"mposDebtId\\\":141513006,\\\"debtAmount\\\":484999},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"7f64c1993957e3374ba9990e4399b2e8","Description":"success"},"ParseError":{"errorCode":1000,"data":"{\"data\":{\"debtStatus\":\"PENDING\",\"merchantId\":22732,\"lendingRequestId\":\"NEXTLENDV4-NL25091861773\",\"mposDebtId\":141513006,\"debtAmount\":484999},\"status\":true,\"error\":{\"code\":1000,\"message\":\"DO_SERVICE_SUCCESS\",\"messageEn\":\"\"}}"}} 
