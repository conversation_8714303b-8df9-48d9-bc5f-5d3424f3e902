<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction;

use Exception;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use App\Lib\TelegramAlert;
use Illuminate\Support\Str;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Ultilities\BatchUtil;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Model\CollectDebtContractLevel;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;

class SendRequestBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$canRunJob = parent::canRunJob();

		if (!$canRunJob) {
			return 'job will for nextday';
		}

		$r = CollectDebtProcessing::query()
    ->from(DB::raw('debt_recovery_processing AS p FORCE INDEX (send_request_idx)'))
    ->join('debt_recovery_contract_level as cl', 'cl.contract_code', '=', 'p.contract_code')
    ->where('p.is_sent_request', 0)
    ->where('p.expired_at', '>=', now())
    ->where('p.push_time', '<=', time())
    ->where('cl.is_send_cmd', 0)
    ->orderBy('cl.id', 'asc')
    ->limit(config('nextlend.BATCHING_LIMIT'))
    ->select([
        'p.id',
        'p.contract_code',
        'cl.id as contract_level_id',
    ])
    ->get();


		if ($r->isEmpty()) {
			return ['msg' => 'No data for SendRequestBatchAction'];
		}

		$r = $r->unique('profile_id')->values();
		$ids = $r->pluck('id')->toArray();

		$ranges = array_chunk($ids, $this->batchSize);

		$this->processBatch($ranges);
		return ['msg' => 'ok done'];
	}

	private function processBatch(array $ranges)
	{
		$client = parent::createHttpClient($this->timeout-10);

		$baseUrl = config('app.url');

		// Generator
		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/HandleSendRequestMpos?ids=%s', $baseUrl, implode(',', $r));
				yield $r => new Request('GET', $url, []);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[SendRequest --->range error: " . json_encode($r);
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return ['msg' => 'all done'];
	}

	public function HandleSendRequestMpos()
	{
		$ids = request()->get('ids');
		$listIds = array_map('intval', explode(',', $ids));

		$listProcessing = CollectDebtProcessing::query()->with('collectDebtRequest')->whereIn('id', $listIds)->get();

		if ($listProcessing->isEmpty()) {
			return 'No data';
		}

		foreach ($listProcessing as $p) {
			try {
				$collectDebtRequest = $p->collectDebtRequest;
				$this->handle($collectDebtRequest);
			}catch(\Throwable $th) {
				$msg = "[SendRequest --->$p->partner_request_id] failed: " . $th->getMessage();
				Log::info($msg);
			}
			
		}

		return "ok";
	}

	

	public function handle(CollectDebtRequest $collectDebtRequest)
	{
		$partnerRequestId = $collectDebtRequest->partner_request_id;

		if (!empty($collectDebtRequest->partner_transaction_id)) {
			$this->markAsSent($partnerRequestId, $collectDebtRequest->contract_code);
			return $partnerRequestId;
		}

		if ($collectDebtRequest->isRequestDaGhiSo()) {
			$this->markAsSent($partnerRequestId, $collectDebtRequest->contract_code);
			return $partnerRequestId;
		}

		if ($collectDebtRequest->time_expired < time()) {
			$this->markAsSent($partnerRequestId, $collectDebtRequest->contract_code);
			return $partnerRequestId;
		}

		$rq = app(SendRequestViaMposSubAction::class)->run($collectDebtRequest);

		if (empty($rq->partner_transaction_id)) {
			$msg = sprintf(
				'env: %s ---- PartnerRequestId: %s ---- Contract: %s dang khong co chung tu', 
				config('app.env'), 
				$partnerRequestId, 
				$collectDebtRequest->contract_code
			);
			
			@TelegramAlert::alertGuiLenhTrich($msg);
			throw new Exception($msg);
		}

		$this->markAsSent($partnerRequestId, $collectDebtRequest->contract_code);

		return $partnerRequestId;
	}

	private function markAsSent(string $partnerRequestId, string $contractCode)
	{
		return DB::transaction(function () use ($partnerRequestId, $contractCode) {	
			$wasUpdateProcessing = CollectDebtProcessing::query()
			->where('partner_request_id', $partnerRequestId)
			->update(['is_sent_request' => 1, 'updated_at' => now(), 'checked_at' => now()->addHours(1)]);
			
			throw_if(!$wasUpdateProcessing, new Exception('Loi cap nhat processing ve da xu ly'));

			$wasUpdateLevel = CollectDebtContractLevel::query()->where('contract_code', $contractCode)->update(['is_send_cmd' => 1, 'updated_at' => now()]);
			throw_if(!$wasUpdateLevel, new Exception('Loi cap nhat contract_level ve da gui lenh'));
		});
	}
}  // End class